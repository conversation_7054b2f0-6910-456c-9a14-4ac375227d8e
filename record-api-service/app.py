"""
记录API服务主应用
"""
from flask import Flask, jsonify, request
from flask_cors import CORS
from config import config
from models import db
import os
import logging
from logging.handlers import RotatingFileHandler

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'production')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    CORS(app)
    
    # 注册蓝图
    register_blueprints(app)

    # 注册路由
    register_routes(app)

    # 注册错误处理器
    register_error_handlers(app)

    # 配置日志
    configure_logging(app)
    
    # 创建数据库表
    with app.app_context():
        try:
            db.create_all()
            app.logger.info('数据库表创建成功')
        except Exception as e:
            app.logger.error(f'数据库表创建失败: {e}')
    
    return app

def register_blueprints(app):
    """注册蓝图"""
    from blueprints.users import users_bp
    from blueprints.conversations import conversations_bp
    from blueprints.messages import messages_bp

    # API版本前缀
    api_prefix = f'/api/{app.config["API_VERSION"]}'

    app.register_blueprint(users_bp, url_prefix=api_prefix)
    app.register_blueprint(conversations_bp, url_prefix=api_prefix)
    app.register_blueprint(messages_bp, url_prefix=api_prefix)

def register_routes(app):
    """注册路由"""
    from datetime import datetime

    # 根路由
    @app.route('/')
    def index():
        """根路径"""
        return jsonify({
            'success': True,
            'service': 'Record API Service',
            'version': app.config.get('API_VERSION', 'v1'),
            'description': '记录管理API服务'
        })

    @app.route('/health')
    def health():
        """健康检查"""
        try:
            # 检查数据库连接
            from sqlalchemy import text
            db.session.execute(text('SELECT 1'))
            return jsonify({
                'success': True,
                'status': 'healthy',
                'service': 'record-api-service',
                'database': 'connected'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'status': 'unhealthy',
                'service': 'record-api-service',
                'database': 'disconnected',
                'error': str(e)
            }), 500

    @app.route('/api/v1/health')
    def api_health():
        """API健康检查"""
        return health()

    @app.route('/api/v1/info')
    def api_info():
        """API信息"""
        return jsonify({
            'success': True,
            'service': 'Record API Service',
            'version': app.config.get('API_VERSION', 'v1'),
            'description': '记录管理API服务',
            'endpoints': {
                'users': '/api/v1/users',
                'conversations': '/api/v1/conversations',
                'messages': '/api/v1/messages'
            },
            'features': [
                '用户管理',
                '对话管理',
                '消息管理',
                '搜索功能',
                '统计信息',
                '批量操作'
            ]
        })

    # 请求前处理
    @app.before_request
    def before_request():
        """请求前处理"""
        # 记录请求信息
        if app.config.get('LOG_LEVEL') == 'DEBUG':
            app.logger.debug(f'请求: {request.method} {request.url}')

    # 请求后处理
    @app.after_request
    def after_request(response):
        """请求后处理"""
        # 添加CORS头
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')

        # 记录响应信息
        if app.config.get('LOG_LEVEL') == 'DEBUG':
            app.logger.debug(f'响应: {response.status_code}')

        return response

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'success': False,
            'error': '请求格式错误'
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({
            'success': False,
            'error': '未授权访问'
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({
            'success': False,
            'error': '权限不足'
        }), 403
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'success': False,
            'error': '资源未找到'
        }), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return jsonify({
            'success': False,
            'error': '方法不允许'
        }), 405
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': '服务器内部错误'
        }), 500

def configure_logging(app):
    """配置日志"""
    if not app.debug and not app.testing:
        # 确保日志目录存在
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 配置文件日志
        file_handler = RotatingFileHandler(
            os.path.join(log_dir, 'record-api-service.log'),
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('记录API服务启动')



# 创建应用实例
app = create_app()

if __name__ == '__main__':
    app.run(
        host=app.config.get('FLASK_RUN_HOST', '0.0.0.0'),
        port=app.config.get('FLASK_RUN_PORT', 5003),
        debug=app.config.get('DEBUG', False)
    )
