#!/usr/bin/env python3
"""
测试管理员账号的脚本
用于验证管理员账号是否正确创建和配置
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:5003"
ADMIN_API_KEY = "admin-api-key-change-in-production"

def test_admin_access():
    """测试管理员账号访问"""
    headers = {
        'Authorization': f'Bearer {ADMIN_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    print("🔍 测试管理员账号访问...")
    print(f"API密钥: {ADMIN_API_KEY}")
    print(f"服务地址: {BASE_URL}")
    print("-" * 50)
    
    # 1. 测试健康检查
    print("1. 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/health")
        if response.status_code == 200:
            print("✅ 服务健康检查通过")
        else:
            print(f"❌ 服务健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        return False
    
    # 2. 测试获取用户列表（需要认证）
    print("\n2. 测试获取用户列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/users", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                users = data.get('data', {}).get('users', [])
                print(f"✅ 成功获取用户列表，共 {len(users)} 个用户")
                
                # 查找管理员账号
                admin_user = None
                for user in users:
                    if user.get('permission') == 9:
                        admin_user = user
                        break
                
                if admin_user:
                    print(f"✅ 找到管理员账号:")
                    print(f"   - ID: {admin_user.get('id')}")
                    print(f"   - API密钥: {admin_user.get('api_key')}")
                    print(f"   - 权限级别: {admin_user.get('permission')}")
                    print(f"   - 激活状态: {admin_user.get('is_active')}")
                    print(f"   - 创建时间: {admin_user.get('created_at')}")
                else:
                    print("❌ 未找到管理员账号")
                    return False
            else:
                print(f"❌ API返回错误: {data.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False
    
    # 3. 测试创建新用户（管理员权限）
    print("\n3. 测试创建新用户...")
    test_user_data = {
        "api_key": "test-user-api-key-123",
        "permission": 1,
        "current_balance": 10.0
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/users", 
            headers=headers, 
            json=test_user_data
        )
        if response.status_code == 201:
            data = response.json()
            if data.get('success'):
                user = data.get('data')
                print(f"✅ 成功创建测试用户:")
                print(f"   - ID: {user.get('id')}")
                print(f"   - API密钥: {user.get('api_key')}")
                print(f"   - 权限级别: {user.get('permission')}")
                
                # 清理：删除测试用户
                user_id = user.get('id')
                delete_response = requests.delete(
                    f"{BASE_URL}/api/v1/users/{user_id}", 
                    headers=headers
                )
                if delete_response.status_code == 200:
                    print("✅ 测试用户已清理")
                else:
                    print("⚠️ 测试用户清理失败，请手动删除")
            else:
                print(f"❌ 创建用户失败: {data.get('error')}")
                return False
        else:
            print(f"❌ 创建用户请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 创建用户异常: {e}")
        return False
    
    print("\n🎉 所有测试通过！管理员账号配置正确。")
    return True

def test_unauthorized_access():
    """测试未授权访问"""
    print("\n4. 测试未授权访问...")
    
    # 使用错误的API密钥
    wrong_headers = {
        'Authorization': 'Bearer wrong-api-key',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/users", headers=wrong_headers)
        if response.status_code == 401:
            print("✅ 未授权访问被正确拒绝")
            return True
        else:
            print(f"❌ 未授权访问未被拒绝: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试未授权访问异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试管理员账号配置...")
    print("=" * 60)
    
    success = test_admin_access()
    if success:
        success = test_unauthorized_access()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！系统配置正确。")
        print("\n📝 下一步:")
        print("1. 在生产环境中修改默认的管理员API密钥")
        print("2. 根据需要创建其他用户账号")
        print("3. 配置适当的权限级别")
    else:
        print("❌ 测试失败，请检查配置。")
        print("\n🔧 故障排除:")
        print("1. 确保 record-api-service 服务正在运行")
        print("2. 确保 records-database 服务正在运行")
        print("3. 检查数据库连接配置")
        print("4. 查看服务日志获取更多信息")
