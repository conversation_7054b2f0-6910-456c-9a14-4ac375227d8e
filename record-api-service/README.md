# 记录API服务 (Record API Service)

这是大语言模型管理系统的记录管理组件：一个专门用于管理用户、对话和消息记录的RESTful API服务，运行在5003端口。

## 核心功能

- **用户管理**：创建、查询、更新和删除用户信息，管理用户权限和余额
- **对话管理**：管理用户的对话会话，包括对话创建、更新和删除
- **消息管理**：存储和检索聊天消息，支持批量操作和搜索功能
- **统计分析**：提供用户和对话的统计信息，包括token使用量和费用统计
- **安全认证**：所有请求必须通过 `Authorization: Bearer <API_KEY>` 头进行认证

## 数据库连接

- **主数据库**: 连接到 `records-database` (端口3307) 的 `vdb_records` 数据库
- **用户**: records_user
- **密码**: records_password

## API 端点概览

### 用户管理 API

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/users` | 获取用户列表 |
| GET | `/api/v1/users/{id}` | 获取单个用户信息 |
| POST | `/api/v1/users` | 创建新用户 |
| PUT | `/api/v1/users/{id}` | 更新用户信息 |
| DELETE | `/api/v1/users/{id}` | 删除用户 |
| GET | `/api/v1/users/by-api-key/{api_key}` | 根据API密钥获取用户 |
| GET | `/api/v1/users/{id}/stats` | 获取用户统计信息 |

### 对话管理 API

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/conversations` | 获取对话列表 |
| GET | `/api/v1/conversations/{id}` | 获取单个对话信息 |
| POST | `/api/v1/conversations` | 创建新对话 |
| PUT | `/api/v1/conversations/{id}` | 更新对话信息 |
| DELETE | `/api/v1/conversations/{id}` | 删除对话 |
| GET | `/api/v1/users/{user_id}/conversations` | 获取用户的对话列表 |
| GET | `/api/v1/conversations/{id}/messages` | 获取对话的消息列表 |
| GET | `/api/v1/conversations/{id}/stats` | 获取对话统计信息 |

### 消息管理 API

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/messages` | 获取消息列表 |
| GET | `/api/v1/messages/{id}` | 获取单个消息信息 |
| POST | `/api/v1/messages` | 创建新消息 |
| PUT | `/api/v1/messages/{id}` | 更新消息信息 |
| DELETE | `/api/v1/messages/{id}` | 删除消息 |
| POST | `/api/v1/messages/batch` | 批量创建消息 |
| GET | `/api/v1/messages/search` | 搜索消息 |
| **PUT** | **`/api/v1/messages/{id}/urls`** | **批量更新消息URL** |
| **DELETE** | **`/api/v1/messages/{id}/content`** | **删除消息时的MinIO清理** |

### 系统 API

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/health` | 健康检查 |
| GET | `/api/v1/health` | API健康检查 |
| GET | `/api/v1/info` | API信息 |

## 认证方式

所有API请求都需要在请求头中包含有效的API密钥：

```
Authorization: Bearer YOUR_API_KEY
```

或者作为查询参数：

```
?api_key=YOUR_API_KEY
```

### 默认管理员账号

系统会自动创建一个默认的管理员账号：

- **API密钥**: `admin-api-key-change-in-production`
- **权限级别**: 9 (管理员)
- **状态**: 激活

⚠️ **重要**: 在生产环境中，请务必修改默认的管理员API密钥！

### 权限级别说明

- **1**: 普通用户 (默认权限)
- **2-8**: 高级用户 (扩展权限)
- **9**: 管理员 (最高权限)

## 响应格式

所有API响应都采用统一的JSON格式：

### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误描述"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "pages": 5,
      "has_prev": false,
      "has_next": true,
      "prev_num": null,
      "next_num": 2
    }
  }
}
```

## 查询参数

### 分页参数
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 20, 最大: 100)

### 排序参数
- `sort_by`: 排序字段
- `sort_order`: 排序方向 (`asc` 或 `desc`)

### 过滤参数
根据不同的API端点，支持不同的过滤参数。

## 使用示例

### 1. 获取用户列表

```bash
curl -X GET "http://localhost:5003/api/v1/users?page=1&per_page=10" \
     -H "Authorization: Bearer admin-api-key-change-in-production"
```

### 2. 创建新用户

```bash
curl -X POST "http://localhost:5003/api/v1/users" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer admin-api-key-change-in-production" \
     -d '{
       "api_key": "user-api-key-123",
       "permission": 1,
       "current_balance": 10.0
     }'
```

### 3. 创建对话

```bash
curl -X POST "http://localhost:5003/api/v1/conversations" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "user_id": 1,
       "title": "新的对话"
     }'
```

### 4. 添加消息

```bash
curl -X POST "http://localhost:5003/api/v1/messages" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "conversation_id": 1,
       "role": "user",
       "content": "你好，请介绍一下自己。",
       "model_id": 1
     }'
```

### 5. 搜索消息

```bash
curl -X GET "http://localhost:5003/api/v1/messages/search?keyword=介绍&page=1" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

### 6. 获取用户统计

```bash
curl -X GET "http://localhost:5003/api/v1/users/1/stats" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

## Python 客户端示例

```python
import requests

class RecordAPIClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def get_users(self, page=1, per_page=20):
        """获取用户列表"""
        response = requests.get(
            f"{self.base_url}/api/v1/users",
            headers=self.headers,
            params={'page': page, 'per_page': per_page}
        )
        return response.json()
    
    def create_user(self, api_key, **kwargs):
        """创建用户"""
        data = {'api_key': api_key, **kwargs}
        response = requests.post(
            f"{self.base_url}/api/v1/users",
            headers=self.headers,
            json=data
        )
        return response.json()
    
    def create_conversation(self, user_id, title=None):
        """创建对话"""
        data = {'user_id': user_id}
        if title:
            data['title'] = title
        response = requests.post(
            f"{self.base_url}/api/v1/conversations",
            headers=self.headers,
            json=data
        )
        return response.json()
    
    def add_message(self, conversation_id, role, content, model_id, **kwargs):
        """添加消息"""
        data = {
            'conversation_id': conversation_id,
            'role': role,
            'content': content,
            'model_id': model_id,
            **kwargs
        }
        response = requests.post(
            f"{self.base_url}/api/v1/messages",
            headers=self.headers,
            json=data
        )
        return response.json()

# 使用示例
client = RecordAPIClient('http://localhost:5003', 'admin-api-key-change-in-production')

# 获取用户列表
users = client.get_users()
print(users)

# 创建用户
new_user = client.create_user('user-123', permission=1, current_balance=10.0)
print(new_user)
```

## 配置

- **端口**: 5003
- **数据库**: 连接到 `records-database` 的 `vdb_records` 数据库
- **核心依赖**: `Flask`, `SQLAlchemy`, `PyMySQL`, `gunicorn`

## 启动服务

### 使用Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export FLASK_ENV=development
export DATABASE_URL=mysql+pymysql://records_user:records_password@localhost:3307/vdb_records

# 启动服务
python app.py
```

### 测试管理员账号

使用提供的测试脚本验证管理员账号是否正确配置：

```bash
# 确保服务正在运行
docker-compose up -d

# 运行测试脚本
python test_admin.py
```

测试脚本会验证：
- 服务健康状态
- 管理员账号是否存在
- 管理员权限是否正确
- 认证机制是否工作正常

## 与其他服务的关系

- **records-database**: 主要数据存储，存储用户、对话和消息数据
- **model-api-service**: 可以调用本服务来记录API调用历史
- **model-manager**: 可以查询本服务获取用户使用统计

## 注意事项

1. **数据库依赖**: 确保 `records-database` 服务已启动并可访问
2. **管理员账号**: 系统会自动创建默认管理员账号，生产环境请务必修改默认API密钥
3. **API密钥管理**: 在生产环境中请使用安全的API密钥
4. **权限控制**: 管理员权限级别为9，拥有所有API的访问权限
5. **数据备份**: 定期备份重要的聊天记录数据
6. **性能监控**: 监控API响应时间和数据库连接状态
7. **日志管理**: 定期清理和归档日志文件

## 详细API文档

### 用户管理 API

#### GET /api/v1/users
获取用户列表，支持分页和过滤。

**查询参数:**
- `page` (int): 页码，默认1
- `per_page` (int): 每页数量，默认20，最大100
- `is_active` (bool): 过滤活跃用户
- `permission` (int): 过滤权限级别
- `sort_by` (string): 排序字段，默认created_at
- `sort_order` (string): 排序方向，asc或desc，默认desc

**响应示例:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "api_key": "user-api-key-123",
        "created_at": "2024-01-01T00:00:00",
        "is_active": true,
        "permission": 1,
        "current_balance": 10.0,
        "total_spent": 5.5,
        "total_prompt_tokens": 1000,
        "total_completion_tokens": 800
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 1,
      "pages": 1,
      "has_prev": false,
      "has_next": false
    }
  }
}
```

#### POST /api/v1/users
创建新用户。

**请求体:**
```json
{
  "api_key": "user-api-key-123",  // 必需
  "is_active": true,              // 可选，默认true
  "permission": 1,                // 可选，默认1
  "mathjax": false,              // 可选，默认false
  "current_model_id": 1,         // 可选
  "current_temperature": 0.7,    // 可选，默认0.7
  "total_deposited": 10.0,       // 可选，默认0.0
  "current_balance": 10.0        // 可选，默认0.0
}
```

#### PUT /api/v1/users/{id}
更新用户信息。

**请求体:** (所有字段都是可选的)
```json
{
  "is_active": false,
  "permission": 2,
  "current_balance": 15.0
}
```

### 对话管理 API

#### GET /api/v1/conversations
获取对话列表。

**查询参数:**
- `page` (int): 页码
- `per_page` (int): 每页数量
- `user_id` (int): 过滤特定用户的对话
- `include_messages` (bool): 是否包含消息，默认false
- `sort_by` (string): 排序字段，默认latest_revised_at
- `sort_order` (string): 排序方向

#### POST /api/v1/conversations
创建新对话。

**请求体:**
```json
{
  "user_id": 1,                    // 必需
  "title": "关于AI的讨论"          // 可选
}
```

### 消息管理 API

#### GET /api/v1/messages
获取消息列表。

**查询参数:**
- `conversation_id` (int): 过滤特定对话的消息
- `role` (string): 过滤消息角色 (user, assistant, system)
- `model_id` (int): 过滤特定模型的消息
- `is_error` (bool): 过滤错误消息

#### POST /api/v1/messages
创建新消息。

**请求体:**
```json
{
  "conversation_id": 1,           // 必需
  "role": "user",                 // 必需
  "content": "你好",              // 必需
  "model_id": 1,                  // 必需
  "temperature": 0.7,             // 可选
  "max_tokens": 1000,             // 可选
  "prompt_tokens": 10,            // 可选
  "completion_tokens": 20,        // 可选
  "prompt_cost": 0.001,           // 可选
  "completion_cost": 0.002,       // 可选
  "total_cost": 0.003,            // 可选
  "is_error": false,              // 可选
  "error_info": null              // 可选
}
```

#### POST /api/v1/messages/batch
批量创建消息。

**请求体:**
```json
{
  "messages": [
    {
      "conversation_id": 1,
      "role": "user",
      "content": "你好",
      "model_id": 1
    },
    {
      "conversation_id": 1,
      "role": "assistant",
      "content": "你好！我是AI助手。",
      "model_id": 1,
      "prompt_tokens": 5,
      "completion_tokens": 15
    }
  ]
}
```

#### GET /api/v1/messages/search
搜索消息内容。

**查询参数:**
- `keyword` (string): 搜索关键词，必需
- `conversation_id` (int): 限制在特定对话中搜索
- `user_id` (int): 限制在特定用户的消息中搜索
- `role` (string): 限制消息角色

## 错误代码

| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 201 | 创建成功 |
| 400 | 请求格式错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源未找到 |
| 405 | 方法不允许 |
| 500 | 服务器内部错误 |

## 数据模型

### User (用户)
```json
{
  "id": 1,
  "api_key": "string",
  "created_at": "datetime",
  "is_active": "boolean",
  "permission": "integer",
  "mathjax": "boolean",
  "current_model_id": "integer",
  "current_temperature": "decimal",
  "current_conversation_id": "integer",
  "total_deposited": "decimal",
  "total_spent": "decimal",
  "current_balance": "decimal",
  "total_prompt_tokens": "integer",
  "total_completion_tokens": "integer"
}
```

### Conversation (对话)
```json
{
  "id": 1,
  "created_at": "datetime",
  "latest_revised_at": "datetime",
  "user_id": "integer",
  "title": "string",
  "message_count": "integer"
}
```

### Message (消息)
```json
{
  "id": 1,
  "conversation_id": "integer",
  "role": "string",
  "content": "text",
  "created_at": "datetime",
  "updated_at": "datetime",
  "model_id": "integer",
  "temperature": "decimal",
  "max_tokens": "integer",
  "prompt_tokens": "integer",
  "completion_tokens": "integer",
  "prompt_cost": "decimal",
  "completion_cost": "decimal",
  "total_cost": "decimal",
  "is_error": "boolean",
  "error_info": "text",
  "original_url": "string",
  "rendered_katex_url": "string",
  "rendered_plain_url": "string"
}
```

## 🆕 MinIO集成功能

### MinIO URL字段说明

消息表新增了三个URL字段，用于存储MinIO对象存储中的文件链接：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `original_url` | VARCHAR(500) | 原始消息内容的MinIO存储URL |
| `rendered_katex_url` | VARCHAR(500) | KaTeX数学公式渲染后的MinIO存储URL |
| `rendered_plain_url` | VARCHAR(500) | 纯文本格式渲染后的MinIO存储URL |

### 新增API端点详细说明

#### PUT /api/v1/messages/{message_id}/urls
批量更新消息的MinIO URL字段。

**请求参数:**
- `message_id` (路径参数): 消息ID

**请求体:**
```json
{
  "original_url": "https://minio.example.com/bucket/original/file123.md",
  "rendered_katex_url": "https://minio.example.com/bucket/katex/file123.html",
  "rendered_plain_url": "https://minio.example.com/bucket/plain/file123.txt"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "original_url": "https://minio.example.com/bucket/original/file123.md",
    "rendered_katex_url": "https://minio.example.com/bucket/katex/file123.html",
    "rendered_plain_url": "https://minio.example.com/bucket/plain/file123.txt",
    "updated_at": "2024-01-01T12:00:00"
  },
  "message": "消息URL已更新"
}
```

**使用示例:**
```bash
curl -X PUT "http://localhost:5003/api/v1/messages/123/urls" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "original_url": "https://minio.example.com/bucket/original/file123.md",
       "rendered_katex_url": "https://minio.example.com/bucket/katex/file123.html"
     }'
```

#### DELETE /api/v1/messages/{message_id}/content
删除消息时的MinIO内容清理接口，用于清理相关的MinIO存储文件。

**请求参数:**
- `message_id` (路径参数): 消息ID

**响应示例:**
```json
{
  "success": true,
  "data": {
    "message_id": 123,
    "urls_to_cleanup": [
      "https://minio.example.com/bucket/original/file123.md",
      "https://minio.example.com/bucket/katex/file123.html",
      "https://minio.example.com/bucket/plain/file123.txt"
    ],
    "cleanup_count": 3
  },
  "message": "消息内容URL已清理"
}
```

**使用示例:**
```bash
curl -X DELETE "http://localhost:5003/api/v1/messages/123/content" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**功能说明:**
- 该接口会收集消息中所有非空的URL字段
- 将数据库中的URL字段设置为NULL
- 返回需要从MinIO中清理的URL列表
- 调用方需要根据返回的URL列表执行实际的MinIO文件删除操作

## 📚 完整API文档

### 🔐 认证说明

所有API请求都需要在请求头中包含有效的API密钥：

```http
Authorization: Bearer YOUR_API_KEY
```

或者作为查询参数：
```http
GET /api/v1/users?api_key=YOUR_API_KEY
```

### 📄 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据内容
  }
}
```

#### 错误响应
```json
{
  "success": false,
  "error": "错误描述信息"
}
```

#### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "pages": 5,
      "has_prev": false,
      "has_next": true,
      "prev_num": null,
      "next_num": 2
    }
  }
}
```

### 🔍 通用查询参数

#### 分页参数
- `page` (int): 页码，默认1
- `per_page` (int): 每页数量，默认20，最大100

#### 排序参数
- `sort_by` (string): 排序字段
- `sort_order` (string): 排序方向，`asc`或`desc`

---

## 👥 用户管理API详细文档

### GET /api/v1/users
获取用户列表，支持分页和过滤。

**查询参数:**
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `page` | int | 1 | 页码 |
| `per_page` | int | 20 | 每页数量（最大100） |
| `is_active` | bool | - | 过滤活跃用户 |
| `permission` | int | - | 过滤权限级别 |
| `sort_by` | string | created_at | 排序字段 |
| `sort_order` | string | desc | 排序方向 |

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/users?page=1&per_page=10&is_active=true" \
     -H "Authorization: Bearer admin-api-key-change-in-production"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "api_key": "admin-api-key-change-in-production",
        "created_at": "2024-01-01T00:00:00",
        "is_active": true,
        "permission": 9,
        "mathjax": false,
        "current_model_id": null,
        "current_temperature": 0.70,
        "current_conversation_id": null,
        "total_deposited": 0.0,
        "total_spent": 0.0,
        "current_balance": 0.0,
        "total_prompt_tokens": 0,
        "total_completion_tokens": 0
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 10,
      "total": 1,
      "pages": 1,
      "has_prev": false,
      "has_next": false,
      "prev_num": null,
      "next_num": null
    }
  }
}
```

### GET /api/v1/users/{user_id}
获取单个用户的详细信息。

**路径参数:**
- `user_id` (int): 用户ID

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/users/1" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "api_key": "user-api-key-123",
    "created_at": "2024-01-01T00:00:00",
    "is_active": true,
    "permission": 1,
    "mathjax": false,
    "current_model_id": 1,
    "current_temperature": 0.70,
    "current_conversation_id": 5,
    "total_deposited": 100.0,
    "total_spent": 25.50,
    "current_balance": 74.50,
    "total_prompt_tokens": 5000,
    "total_completion_tokens": 3500
  }
}
```

### POST /api/v1/users
创建新用户。

**请求体:**
```json
{
  "api_key": "user-api-key-123",        // 必需，唯一
  "is_active": true,                    // 可选，默认true
  "permission": 1,                      // 可选，默认1
  "mathjax": false,                     // 可选，默认false
  "current_model_id": 1,                // 可选
  "current_temperature": 0.7,           // 可选，默认0.7
  "total_deposited": 100.0,             // 可选，默认0.0
  "current_balance": 100.0              // 可选，默认0.0
}
```

**请求示例:**
```bash
curl -X POST "http://localhost:5003/api/v1/users" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer admin-api-key-change-in-production" \
     -d '{
       "api_key": "user-api-key-123",
       "permission": 1,
       "current_balance": 50.0
     }'
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "api_key": "user-api-key-123",
    "created_at": "2024-01-01T12:00:00",
    "is_active": true,
    "permission": 1,
    "mathjax": false,
    "current_model_id": null,
    "current_temperature": 0.70,
    "current_conversation_id": null,
    "total_deposited": 0.0,
    "total_spent": 0.0,
    "current_balance": 50.0,
    "total_prompt_tokens": 0,
    "total_completion_tokens": 0
  }
}
```

### PUT /api/v1/users/{user_id}
更新用户信息。

**路径参数:**
- `user_id` (int): 用户ID

**请求体:** (所有字段都是可选的)
```json
{
  "is_active": false,
  "permission": 2,
  "current_balance": 75.0,
  "current_model_id": 2,
  "current_temperature": 0.8
}
```

**请求示例:**
```bash
curl -X PUT "http://localhost:5003/api/v1/users/2" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer admin-api-key-change-in-production" \
     -d '{
       "current_balance": 75.0,
       "permission": 2
     }'
```

### DELETE /api/v1/users/{user_id}
删除用户（会级联删除相关的对话和消息）。

**路径参数:**
- `user_id` (int): 用户ID

**请求示例:**
```bash
curl -X DELETE "http://localhost:5003/api/v1/users/2" \
     -H "Authorization: Bearer admin-api-key-change-in-production"
```

**响应示例:**
```json
{
  "success": true,
  "message": "用户已删除"
}
```

### GET /api/v1/users/by-api-key/{api_key}
根据API密钥获取用户信息。

**路径参数:**
- `api_key` (string): 用户的API密钥

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/users/by-api-key/user-api-key-123" \
     -H "Authorization: Bearer admin-api-key-change-in-production"
```

### GET /api/v1/users/{user_id}/stats
获取用户的统计信息。

**路径参数:**
- `user_id` (int): 用户ID

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/users/1/stats" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "user_id": 1,
    "total_conversations": 15,
    "total_messages": 150,
    "total_prompt_tokens": 5000,
    "total_completion_tokens": 3500,
    "total_cost": 25.50,
    "current_balance": 74.50,
    "average_tokens_per_message": 56.67,
    "last_activity": "2024-01-01T12:00:00"
  }
}
```

---

## 💬 对话管理API详细文档

### GET /api/v1/conversations
获取对话列表，支持分页和过滤。

**查询参数:**
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `page` | int | 1 | 页码 |
| `per_page` | int | 20 | 每页数量 |
| `user_id` | int | - | 过滤特定用户的对话 |
| `include_messages` | bool | false | 是否包含消息列表 |
| `sort_by` | string | latest_revised_at | 排序字段 |
| `sort_order` | string | desc | 排序方向 |

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/conversations?user_id=1&include_messages=true" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "id": 1,
        "created_at": "2024-01-01T10:00:00",
        "latest_revised_at": "2024-01-01T12:00:00",
        "user_id": 1,
        "title": "关于AI的讨论",
        "message_count": 5,
        "messages": [
          {
            "id": 1,
            "role": "user",
            "content": "你好",
            "created_at": "2024-01-01T10:00:00",
            "model_id": 1
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 1,
      "pages": 1,
      "has_prev": false,
      "has_next": false
    }
  }
}
```

### GET /api/v1/conversations/{conversation_id}
获取单个对话的详细信息。

**路径参数:**
- `conversation_id` (int): 对话ID

**查询参数:**
- `include_messages` (bool): 是否包含消息列表，默认false

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/conversations/1?include_messages=true" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

### POST /api/v1/conversations
创建新对话。

**请求体:**
```json
{
  "user_id": 1,                    // 必需
  "title": "新的对话主题"          // 可选
}
```

**请求示例:**
```bash
curl -X POST "http://localhost:5003/api/v1/conversations" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "user_id": 1,
       "title": "关于机器学习的讨论"
     }'
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "created_at": "2024-01-01T12:00:00",
    "latest_revised_at": "2024-01-01T12:00:00",
    "user_id": 1,
    "title": "关于机器学习的讨论",
    "message_count": 0
  }
}
```

### PUT /api/v1/conversations/{conversation_id}
更新对话信息。

**路径参数:**
- `conversation_id` (int): 对话ID

**请求体:**
```json
{
  "title": "更新后的对话标题"      // 可选
}
```

**请求示例:**
```bash
curl -X PUT "http://localhost:5003/api/v1/conversations/2" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "title": "深度学习技术讨论"
     }'
```

### DELETE /api/v1/conversations/{conversation_id}
删除对话（会级联删除相关的消息）。

**路径参数:**
- `conversation_id` (int): 对话ID

**请求示例:**
```bash
curl -X DELETE "http://localhost:5003/api/v1/conversations/2" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

### GET /api/v1/users/{user_id}/conversations
获取特定用户的对话列表。

**路径参数:**
- `user_id` (int): 用户ID

**查询参数:**
- `page` (int): 页码
- `per_page` (int): 每页数量
- `include_messages` (bool): 是否包含消息

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/users/1/conversations" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

### GET /api/v1/conversations/{conversation_id}/messages
获取对话的消息列表。

**路径参数:**
- `conversation_id` (int): 对话ID

**查询参数:**
- `page` (int): 页码
- `per_page` (int): 每页数量
- `sort_by` (string): 排序字段，默认created_at
- `sort_order` (string): 排序方向，默认asc

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/conversations/1/messages?page=1&per_page=50" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

### GET /api/v1/conversations/{conversation_id}/stats
获取对话的统计信息。

**路径参数:**
- `conversation_id` (int): 对话ID

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/conversations/1/stats" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "conversation_id": 1,
    "message_count": 10,
    "total_prompt_tokens": 500,
    "total_completion_tokens": 750,
    "total_cost": 2.50,
    "first_message_at": "2024-01-01T10:00:00",
    "last_message_at": "2024-01-01T12:00:00",
    "duration_minutes": 120,
    "average_tokens_per_message": 125
  }
}
```

---

## 💬 消息管理API详细文档

### GET /api/v1/messages
获取消息列表，支持分页和多种过滤条件。

**查询参数:**
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `page` | int | 1 | 页码 |
| `per_page` | int | 50 | 每页数量（最大100） |
| `conversation_id` | int | - | 过滤特定对话的消息 |
| `role` | string | - | 过滤消息角色（user/assistant/system） |
| `model_id` | int | - | 过滤特定模型的消息 |
| `is_error` | bool | - | 过滤错误消息 |
| `sort_by` | string | created_at | 排序字段 |
| `sort_order` | string | desc | 排序方向 |

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/messages?conversation_id=1&role=user&page=1" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "id": 1,
        "conversation_id": 1,
        "role": "user",
        "content": "你好，请介绍一下自己。",
        "created_at": "2024-01-01T10:00:00",
        "updated_at": "2024-01-01T10:00:00",
        "model_id": 1,
        "temperature": 0.7,
        "max_tokens": 1000,
        "prompt_tokens": 10,
        "completion_tokens": 0,
        "prompt_cost": 0.001,
        "completion_cost": 0.0,
        "total_cost": 0.001,
        "is_error": false,
        "error_info": null,
        "original_url": null,
        "rendered_katex_url": null,
        "rendered_plain_url": null
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 50,
      "total": 1,
      "pages": 1,
      "has_prev": false,
      "has_next": false
    }
  }
}
```

### GET /api/v1/messages/{message_id}
获取单个消息的详细信息。

**路径参数:**
- `message_id` (int): 消息ID

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/messages/1" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "conversation_id": 1,
    "role": "user",
    "content": "你好，请介绍一下自己。",
    "created_at": "2024-01-01T10:00:00",
    "updated_at": "2024-01-01T10:00:00",
    "model_id": 1,
    "temperature": 0.7,
    "max_tokens": 1000,
    "prompt_tokens": 10,
    "completion_tokens": 0,
    "prompt_cost": 0.001,
    "completion_cost": 0.0,
    "total_cost": 0.001,
    "is_error": false,
    "error_info": null,
    "original_url": "https://minio.example.com/bucket/original/msg1.md",
    "rendered_katex_url": "https://minio.example.com/bucket/katex/msg1.html",
    "rendered_plain_url": "https://minio.example.com/bucket/plain/msg1.txt"
  }
}
```

### POST /api/v1/messages
创建新消息。

**请求体:**
```json
{
  "conversation_id": 1,                    // 必需
  "role": "user",                          // 必需（user/assistant/system）
  "content": "你好，请介绍一下自己。",      // 必需
  "model_id": 1,                           // 必需
  "temperature": 0.7,                      // 可选
  "max_tokens": 1000,                      // 可选
  "prompt_tokens": 10,                     // 可选
  "completion_tokens": 20,                 // 可选
  "prompt_cost": 0.001,                    // 可选
  "completion_cost": 0.002,                // 可选
  "total_cost": 0.003,                     // 可选
  "is_error": false,                       // 可选，默认false
  "error_info": null,                      // 可选
  "original_url": "https://minio.example.com/bucket/original/msg1.md",     // 可选
  "rendered_katex_url": "https://minio.example.com/bucket/katex/msg1.html", // 可选
  "rendered_plain_url": "https://minio.example.com/bucket/plain/msg1.txt"   // 可选
}
```

**请求示例:**
```bash
curl -X POST "http://localhost:5003/api/v1/messages" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "conversation_id": 1,
       "role": "user",
       "content": "你好，请介绍一下自己。",
       "model_id": 1,
       "temperature": 0.7
     }'
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "conversation_id": 1,
    "role": "user",
    "content": "你好，请介绍一下自己。",
    "created_at": "2024-01-01T12:00:00",
    "updated_at": "2024-01-01T12:00:00",
    "model_id": 1,
    "temperature": 0.7,
    "max_tokens": null,
    "prompt_tokens": null,
    "completion_tokens": null,
    "prompt_cost": 0.0,
    "completion_cost": 0.0,
    "total_cost": 0.0,
    "is_error": false,
    "error_info": null,
    "original_url": null,
    "rendered_katex_url": null,
    "rendered_plain_url": null
  }
}
```

### PUT /api/v1/messages/{message_id}
更新消息信息。

**路径参数:**
- `message_id` (int): 消息ID

**请求体:** (所有字段都是可选的)
```json
{
  "content": "更新后的消息内容",
  "role": "assistant",
  "temperature": 0.8,
  "prompt_tokens": 15,
  "completion_tokens": 25,
  "total_cost": 0.005,
  "original_url": "https://minio.example.com/bucket/original/updated_msg.md"
}
```

**请求示例:**
```bash
curl -X PUT "http://localhost:5003/api/v1/messages/2" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "content": "更新后的消息内容",
       "prompt_tokens": 15,
       "completion_tokens": 25
     }'
```

### DELETE /api/v1/messages/{message_id}
删除消息。

**路径参数:**
- `message_id` (int): 消息ID

**请求示例:**
```bash
curl -X DELETE "http://localhost:5003/api/v1/messages/2" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "message": "消息已删除"
}
```

### POST /api/v1/messages/batch
批量创建消息。

**请求体:**
```json
{
  "messages": [
    {
      "conversation_id": 1,
      "role": "user",
      "content": "你好",
      "model_id": 1
    },
    {
      "conversation_id": 1,
      "role": "assistant",
      "content": "你好！我是AI助手，很高兴为您服务。",
      "model_id": 1,
      "prompt_tokens": 5,
      "completion_tokens": 15,
      "total_cost": 0.002
    }
  ]
}
```

**请求示例:**
```bash
curl -X POST "http://localhost:5003/api/v1/messages/batch" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "messages": [
         {
           "conversation_id": 1,
           "role": "user",
           "content": "你好",
           "model_id": 1
         },
         {
           "conversation_id": 1,
           "role": "assistant",
           "content": "你好！我是AI助手。",
           "model_id": 1,
           "prompt_tokens": 5,
           "completion_tokens": 15
         }
       ]
     }'
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "created_count": 2,
    "messages": [
      {
        "id": 3,
        "conversation_id": 1,
        "role": "user",
        "content": "你好",
        "created_at": "2024-01-01T12:30:00",
        "model_id": 1
      },
      {
        "id": 4,
        "conversation_id": 1,
        "role": "assistant",
        "content": "你好！我是AI助手。",
        "created_at": "2024-01-01T12:30:00",
        "model_id": 1,
        "prompt_tokens": 5,
        "completion_tokens": 15
      }
    ]
  }
}
```

### GET /api/v1/messages/search
搜索消息内容。

**查询参数:**
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `keyword` | string | - | 搜索关键词（必需） |
| `page` | int | 1 | 页码 |
| `per_page` | int | 20 | 每页数量（最大100） |
| `conversation_id` | int | - | 限制在特定对话中搜索 |
| `user_id` | int | - | 限制在特定用户的消息中搜索 |
| `role` | string | - | 限制消息角色 |

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/messages/search?keyword=AI&conversation_id=1" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "keyword": "AI",
    "messages": [
      {
        "id": 4,
        "conversation_id": 1,
        "role": "assistant",
        "content": "你好！我是AI助手，很高兴为您服务。",
        "created_at": "2024-01-01T12:30:00",
        "model_id": 1
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 1,
      "pages": 1,
      "has_prev": false,
      "has_next": false
    }
  }
}
```

---

## 🔧 系统API详细文档

### GET /health
基础健康检查端点。

**请求示例:**
```bash
curl -X GET "http://localhost:5003/health"
```

**响应示例:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00"
}
```

### GET /api/v1/health
API健康检查端点，包含数据库连接状态。

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/health" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "database": "connected",
    "timestamp": "2024-01-01T12:00:00",
    "version": "1.1.0"
  }
}
```

### GET /api/v1/info
获取API服务信息。

**请求示例:**
```bash
curl -X GET "http://localhost:5003/api/v1/info" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "service_name": "Record API Service",
    "version": "1.1.0",
    "description": "大语言模型管理系统的记录管理组件",
    "features": [
      "用户管理",
      "对话管理",
      "消息管理",
      "MinIO集成",
      "统计分析"
    ],
    "database_version": "1.1.0",
    "supported_endpoints": 25
  }
}
```

---

## ⚠️ 错误处理

### HTTP状态码

| 状态码 | 说明 | 示例场景 |
|--------|------|----------|
| 200 | 成功 | 正常的GET、PUT请求 |
| 201 | 创建成功 | POST请求成功创建资源 |
| 400 | 请求格式错误 | JSON格式错误、缺少必需字段 |
| 401 | 未授权访问 | API密钥无效或缺失 |
| 403 | 权限不足 | 用户权限级别不够 |
| 404 | 资源未找到 | 请求的用户、对话或消息不存在 |
| 405 | 方法不允许 | 使用了不支持的HTTP方法 |
| 500 | 服务器内部错误 | 数据库连接失败、代码异常 |

### 错误响应格式

```json
{
  "success": false,
  "error": "具体的错误描述信息"
}
```

### 常见错误示例

#### 1. 认证失败
```json
{
  "success": false,
  "error": "无效的API密钥"
}
```

#### 2. 资源不存在
```json
{
  "success": false,
  "error": "用户不存在"
}
```

#### 3. 请求格式错误
```json
{
  "success": false,
  "error": "缺少必需字段: api_key"
}
```

#### 4. 数据库错误
```json
{
  "success": false,
  "error": "数据库连接失败"
}
```

---

## 🐍 Python客户端SDK

### 完整的Python客户端示例

```python
import requests
import json
from typing import Optional, Dict, List, Any

class RecordAPIClient:
    """Record API Service Python客户端"""

    def __init__(self, base_url: str, api_key: str):
        """
        初始化客户端

        Args:
            base_url: API服务地址，如 http://localhost:5003
            api_key: API密钥
        """
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

    def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        response = requests.request(method, url, headers=self.headers, **kwargs)
        response.raise_for_status()
        return response.json()

    # 用户管理方法
    def get_users(self, page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取用户列表"""
        params = {'page': page, 'per_page': per_page, **filters}
        return self._request('GET', '/api/v1/users', params=params)

    def get_user(self, user_id: int) -> Dict[str, Any]:
        """获取单个用户信息"""
        return self._request('GET', f'/api/v1/users/{user_id}')

    def create_user(self, api_key: str, **kwargs) -> Dict[str, Any]:
        """创建用户"""
        data = {'api_key': api_key, **kwargs}
        return self._request('POST', '/api/v1/users', json=data)

    def update_user(self, user_id: int, **kwargs) -> Dict[str, Any]:
        """更新用户信息"""
        return self._request('PUT', f'/api/v1/users/{user_id}', json=kwargs)

    def delete_user(self, user_id: int) -> Dict[str, Any]:
        """删除用户"""
        return self._request('DELETE', f'/api/v1/users/{user_id}')

    def get_user_by_api_key(self, api_key: str) -> Dict[str, Any]:
        """根据API密钥获取用户"""
        return self._request('GET', f'/api/v1/users/by-api-key/{api_key}')

    def get_user_stats(self, user_id: int) -> Dict[str, Any]:
        """获取用户统计信息"""
        return self._request('GET', f'/api/v1/users/{user_id}/stats')

    # 对话管理方法
    def get_conversations(self, page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取对话列表"""
        params = {'page': page, 'per_page': per_page, **filters}
        return self._request('GET', '/api/v1/conversations', params=params)

    def get_conversation(self, conversation_id: int, include_messages: bool = False) -> Dict[str, Any]:
        """获取单个对话信息"""
        params = {'include_messages': include_messages}
        return self._request('GET', f'/api/v1/conversations/{conversation_id}', params=params)

    def create_conversation(self, user_id: int, title: Optional[str] = None) -> Dict[str, Any]:
        """创建对话"""
        data = {'user_id': user_id}
        if title:
            data['title'] = title
        return self._request('POST', '/api/v1/conversations', json=data)

    def update_conversation(self, conversation_id: int, **kwargs) -> Dict[str, Any]:
        """更新对话信息"""
        return self._request('PUT', f'/api/v1/conversations/{conversation_id}', json=kwargs)

    def delete_conversation(self, conversation_id: int) -> Dict[str, Any]:
        """删除对话"""
        return self._request('DELETE', f'/api/v1/conversations/{conversation_id}')

    def get_user_conversations(self, user_id: int, **kwargs) -> Dict[str, Any]:
        """获取用户的对话列表"""
        return self._request('GET', f'/api/v1/users/{user_id}/conversations', params=kwargs)

    def get_conversation_messages(self, conversation_id: int, **kwargs) -> Dict[str, Any]:
        """获取对话的消息列表"""
        return self._request('GET', f'/api/v1/conversations/{conversation_id}/messages', params=kwargs)

    def get_conversation_stats(self, conversation_id: int) -> Dict[str, Any]:
        """获取对话统计信息"""
        return self._request('GET', f'/api/v1/conversations/{conversation_id}/stats')

    # 消息管理方法
    def get_messages(self, page: int = 1, per_page: int = 50, **filters) -> Dict[str, Any]:
        """获取消息列表"""
        params = {'page': page, 'per_page': per_page, **filters}
        return self._request('GET', '/api/v1/messages', params=params)

    def get_message(self, message_id: int) -> Dict[str, Any]:
        """获取单个消息信息"""
        return self._request('GET', f'/api/v1/messages/{message_id}')

    def create_message(self, conversation_id: int, role: str, content: str,
                      model_id: int, **kwargs) -> Dict[str, Any]:
        """创建消息"""
        data = {
            'conversation_id': conversation_id,
            'role': role,
            'content': content,
            'model_id': model_id,
            **kwargs
        }
        return self._request('POST', '/api/v1/messages', json=data)

    def update_message(self, message_id: int, **kwargs) -> Dict[str, Any]:
        """更新消息信息"""
        return self._request('PUT', f'/api/v1/messages/{message_id}', json=kwargs)

    def delete_message(self, message_id: int) -> Dict[str, Any]:
        """删除消息"""
        return self._request('DELETE', f'/api/v1/messages/{message_id}')

    def create_messages_batch(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量创建消息"""
        data = {'messages': messages}
        return self._request('POST', '/api/v1/messages/batch', json=data)

    def search_messages(self, keyword: str, **filters) -> Dict[str, Any]:
        """搜索消息"""
        params = {'keyword': keyword, **filters}
        return self._request('GET', '/api/v1/messages/search', params=params)

    # MinIO相关方法
    def update_message_urls(self, message_id: int, **urls) -> Dict[str, Any]:
        """更新消息的MinIO URL"""
        return self._request('PUT', f'/api/v1/messages/{message_id}/urls', json=urls)

    def cleanup_message_content(self, message_id: int) -> Dict[str, Any]:
        """清理消息的MinIO内容"""
        return self._request('DELETE', f'/api/v1/messages/{message_id}/content')

    # 系统方法
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return self._request('GET', '/api/v1/health')

    def get_info(self) -> Dict[str, Any]:
        """获取API信息"""
        return self._request('GET', '/api/v1/info')

# 使用示例
if __name__ == "__main__":
    # 初始化客户端
    client = RecordAPIClient(
        base_url='http://localhost:5003',
        api_key='admin-api-key-change-in-production'
    )

    try:
        # 健康检查
        health = client.health_check()
        print("服务状态:", health)

        # 获取用户列表
        users = client.get_users(page=1, per_page=10)
        print("用户数量:", users['data']['pagination']['total'])

        # 创建用户
        new_user = client.create_user(
            api_key='test-user-123',
            permission=1,
            current_balance=50.0
        )
        user_id = new_user['data']['id']
        print("创建用户ID:", user_id)

        # 创建对话
        conversation = client.create_conversation(
            user_id=user_id,
            title='测试对话'
        )
        conversation_id = conversation['data']['id']
        print("创建对话ID:", conversation_id)

        # 创建消息
        message = client.create_message(
            conversation_id=conversation_id,
            role='user',
            content='你好，这是一条测试消息。',
            model_id=1,
            original_url='https://minio.example.com/bucket/test.md'
        )
        message_id = message['data']['id']
        print("创建消息ID:", message_id)

        # 更新消息URL
        url_update = client.update_message_urls(
            message_id=message_id,
            rendered_katex_url='https://minio.example.com/bucket/katex/test.html',
            rendered_plain_url='https://minio.example.com/bucket/plain/test.txt'
        )
        print("URL更新结果:", url_update['message'])

        # 搜索消息
        search_results = client.search_messages(keyword='测试')
        print("搜索结果数量:", len(search_results['data']['messages']))

        # 获取统计信息
        user_stats = client.get_user_stats(user_id)
        print("用户统计:", user_stats['data'])

    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")
```

---

## 🚀 部署指南

### Docker部署（推荐）

#### 1. 使用Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  record-api-service:
    build: .
    ports:
      - "5003:5003"
    environment:
      - DATABASE_URL=mysql+pymysql://records_user:records_password@records-db:3306/vdb_records
      - FLASK_ENV=production
    depends_on:
      - records-db
    networks:
      - vdb_network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  records-db:
    image: mysql:8.0
    ports:
      - "3307:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=vdb_records
      - MYSQL_USER=records_user
      - MYSQL_PASSWORD=records_password
    volumes:
      - /vdb_records:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - vdb_network
    restart: unless-stopped

networks:
  vdb_network:
    driver: bridge
```

#### 2. 启动服务

```bash
# 创建必要的目录
mkdir -p logs
sudo mkdir -p /vdb_records
sudo chown -R 999:999 /vdb_records

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f record-api-service
```

#### 3. 验证部署

```bash
# 健康检查
curl http://localhost:5003/health

# API健康检查
curl -H "Authorization: Bearer admin-api-key-change-in-production" \
     http://localhost:5003/api/v1/health

# 获取API信息
curl -H "Authorization: Bearer admin-api-key-change-in-production" \
     http://localhost:5003/api/v1/info
```

### 本地开发部署

#### 1. 环境准备

```bash
# 克隆代码
git clone <repository_url>
cd record-api-service

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 2. 配置环境变量

```bash
# 创建 .env 文件
cat > .env << EOF
FLASK_ENV=development
DATABASE_URL=mysql+pymysql://records_user:records_password@localhost:3307/vdb_records
FLASK_DEBUG=True
LOG_LEVEL=DEBUG
EOF
```

#### 3. 启动开发服务器

```bash
# 确保数据库服务已启动
docker-compose -f ../records-database/docker-compose.yml up -d

# 启动API服务
python app.py

# 或使用Flask命令
flask run --host=0.0.0.0 --port=5003
```

### 生产环境部署

#### 1. 使用Gunicorn

```bash
# 安装Gunicorn
pip install gunicorn

# 启动生产服务器
gunicorn --bind 0.0.0.0:5003 --workers 4 --timeout 120 wsgi:app

# 或使用配置文件
gunicorn --config gunicorn.conf.py wsgi:app
```

#### 2. Gunicorn配置文件

```python
# gunicorn.conf.py
bind = "0.0.0.0:5003"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 120
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
accesslog = "logs/access.log"
errorlog = "logs/error.log"
loglevel = "info"
```

#### 3. Nginx反向代理

```nginx
# /etc/nginx/sites-available/record-api
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5003;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 健康检查端点
    location /health {
        proxy_pass http://127.0.0.1:5003/health;
        access_log off;
    }
}
```

---

## 📊 监控与日志

### 应用监控

#### 1. 健康检查监控

```bash
#!/bin/bash
# health_monitor.sh

API_URL="http://localhost:5003"
API_KEY="your-api-key"

# 基础健康检查
health_status=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/health")
if [ "$health_status" != "200" ]; then
    echo "$(date): 基础健康检查失败 - HTTP $health_status"
    exit 1
fi

# API健康检查
api_health=$(curl -s -H "Authorization: Bearer $API_KEY" "$API_URL/api/v1/health")
if ! echo "$api_health" | grep -q '"success": true'; then
    echo "$(date): API健康检查失败 - $api_health"
    exit 1
fi

echo "$(date): 服务健康状态正常"
```

#### 2. 性能监控

```python
# performance_monitor.py
import requests
import time
import json
from datetime import datetime

class PerformanceMonitor:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {api_key}'}

    def test_endpoint_performance(self, endpoint, method='GET', data=None):
        """测试端点性能"""
        start_time = time.time()
        try:
            if method == 'GET':
                response = requests.get(f"{self.base_url}{endpoint}", headers=self.headers)
            elif method == 'POST':
                response = requests.post(f"{self.base_url}{endpoint}",
                                       headers=self.headers, json=data)

            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 毫秒

            return {
                'endpoint': endpoint,
                'method': method,
                'status_code': response.status_code,
                'response_time_ms': round(response_time, 2),
                'success': response.status_code < 400,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'endpoint': endpoint,
                'method': method,
                'error': str(e),
                'success': False,
                'timestamp': datetime.now().isoformat()
            }

    def run_performance_tests(self):
        """运行性能测试套件"""
        tests = [
            ('/health', 'GET'),
            ('/api/v1/health', 'GET'),
            ('/api/v1/info', 'GET'),
            ('/api/v1/users', 'GET'),
            ('/api/v1/conversations', 'GET'),
            ('/api/v1/messages', 'GET'),
        ]

        results = []
        for endpoint, method in tests:
            result = self.test_endpoint_performance(endpoint, method)
            results.append(result)
            print(f"{endpoint} ({method}): {result.get('response_time_ms', 'ERROR')}ms")

        return results

# 使用示例
if __name__ == "__main__":
    monitor = PerformanceMonitor('http://localhost:5003', 'your-api-key')
    results = monitor.run_performance_tests()

    # 保存结果
    with open(f'performance_report_{int(time.time())}.json', 'w') as f:
        json.dump(results, f, indent=2)
```

### 日志管理

#### 1. 日志配置

```python
# logging_config.py
import logging
import logging.handlers
import os

def setup_logging(app):
    """配置应用日志"""

    # 创建日志目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 设置日志级别
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    app.logger.setLevel(getattr(logging, log_level))

    # 文件处理器 - 应用日志
    file_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'app.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.INFO)

    # 文件处理器 - 错误日志
    error_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'error.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG if app.debug else logging.INFO)

    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s %(levelname)s [%(name)s] %(message)s'
    )
    file_handler.setFormatter(formatter)
    error_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    app.logger.addHandler(file_handler)
    app.logger.addHandler(error_handler)
    app.logger.addHandler(console_handler)

    # 请求日志
    access_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'access.log'),
        maxBytes=10*1024*1024,
        backupCount=5
    )
    access_formatter = logging.Formatter(
        '%(asctime)s %(remote_addr)s "%(method)s %(url)s" %(status_code)s %(response_time)sms'
    )
    access_handler.setFormatter(access_formatter)

    return app.logger
```

#### 2. 日志分析脚本

```bash
#!/bin/bash
# log_analyzer.sh

LOG_DIR="logs"
DATE=$(date +%Y-%m-%d)

echo "=== Record API Service 日志分析报告 ($DATE) ==="
echo

# 错误统计
echo "1. 错误统计:"
if [ -f "$LOG_DIR/error.log" ]; then
    echo "   总错误数: $(wc -l < $LOG_DIR/error.log)"
    echo "   今日错误数: $(grep "$DATE" $LOG_DIR/error.log | wc -l)"
    echo "   错误类型分布:"
    grep "$DATE" $LOG_DIR/error.log | awk '{print $3}' | sort | uniq -c | sort -nr | head -5
else
    echo "   无错误日志文件"
fi
echo

# 访问统计
echo "2. 访问统计:"
if [ -f "$LOG_DIR/access.log" ]; then
    echo "   总请求数: $(wc -l < $LOG_DIR/access.log)"
    echo "   今日请求数: $(grep "$DATE" $LOG_DIR/access.log | wc -l)"
    echo "   热门端点:"
    grep "$DATE" $LOG_DIR/access.log | awk '{print $4}' | sort | uniq -c | sort -nr | head -5
else
    echo "   无访问日志文件"
fi
echo

# 性能统计
echo "3. 性能统计:"
if [ -f "$LOG_DIR/access.log" ]; then
    echo "   平均响应时间: $(grep "$DATE" $LOG_DIR/access.log | awk '{sum+=$6; count++} END {if(count>0) print sum/count "ms"; else print "N/A"}')"
    echo "   最慢请求:"
    grep "$DATE" $LOG_DIR/access.log | sort -k6 -nr | head -3
else
    echo "   无性能数据"
fi
```

---

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 服务启动失败

**问题**: 容器启动失败或立即退出

**排查步骤**:
```bash
# 查看容器状态
docker-compose ps

# 查看启动日志
docker-compose logs record-api-service

# 检查端口占用
netstat -tulpn | grep 5003

# 检查数据库连接
docker-compose exec record-api-service python -c "
import pymysql
try:
    conn = pymysql.connect(host='records-db', user='records_user', password='records_password', database='vdb_records')
    print('数据库连接成功')
    conn.close()
except Exception as e:
    print(f'数据库连接失败: {e}')
"
```

**解决方案**:
- 确保数据库服务已启动
- 检查环境变量配置
- 验证网络连接
- 检查文件权限

#### 2. API请求失败

**问题**: API返回401、403或500错误

**排查步骤**:
```bash
# 测试基础连接
curl -v http://localhost:5003/health

# 测试API认证
curl -v -H "Authorization: Bearer admin-api-key-change-in-production" \
     http://localhost:5003/api/v1/health

# 查看应用日志
docker-compose logs -f record-api-service

# 检查数据库状态
docker-compose exec records-db mysql -u records_user -p -e "SELECT 1;"
```

**解决方案**:
- 验证API密钥正确性
- 检查用户权限级别
- 确认数据库连接正常
- 查看详细错误日志

#### 3. 性能问题

**问题**: API响应缓慢或超时

**排查步骤**:
```bash
# 检查系统资源
docker stats

# 检查数据库性能
docker-compose exec records-db mysql -u records_user -p -e "SHOW PROCESSLIST;"

# 分析慢查询
docker-compose exec records-db mysql -u records_user -p -e "
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;
SHOW VARIABLES LIKE 'slow_query_log%';
"

# 检查索引使用情况
docker-compose exec records-db mysql -u records_user -p -e "
USE vdb_records;
EXPLAIN SELECT * FROM messages WHERE conversation_id = 1;
"
```

**解决方案**:
- 增加数据库连接池大小
- 优化数据库查询和索引
- 增加服务器资源
- 启用缓存机制

#### 4. 数据不一致

**问题**: 数据查询结果异常或不一致

**排查步骤**:
```bash
# 检查数据库完整性
docker-compose exec records-db mysql -u records_user -p -e "
USE vdb_records;
CHECK TABLE users;
CHECK TABLE conversations;
CHECK TABLE messages;
"

# 检查外键约束
docker-compose exec records-db mysql -u records_user -p -e "
USE vdb_records;
SELECT * FROM information_schema.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = 'vdb_records';
"

# 验证数据一致性
docker-compose exec records-db mysql -u records_user -p -e "
USE vdb_records;
SELECT
    c.id as conversation_id,
    c.user_id,
    COUNT(m.id) as message_count
FROM conversations c
LEFT JOIN messages m ON c.id = m.conversation_id
GROUP BY c.id, c.user_id;
"
```

**解决方案**:
- 修复数据库表
- 重建索引
- 检查应用逻辑
- 从备份恢复数据

### 紧急恢复程序

#### 1. 服务快速重启

```bash
#!/bin/bash
# emergency_restart.sh

echo "开始紧急重启程序..."

# 停止服务
docker-compose down

# 清理容器和网络
docker system prune -f

# 重新启动
docker-compose up -d

# 等待服务启动
sleep 30

# 健康检查
if curl -f http://localhost:5003/health; then
    echo "服务重启成功"
else
    echo "服务重启失败，请检查日志"
    docker-compose logs
fi
```

#### 2. 数据库恢复

```bash
#!/bin/bash
# database_recovery.sh

BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file.sql>"
    exit 1
fi

echo "开始数据库恢复..."

# 停止API服务
docker-compose stop record-api-service

# 恢复数据库
docker-compose exec -T records-db mysql -u records_user -p vdb_records < "$BACKUP_FILE"

# 重启API服务
docker-compose start record-api-service

echo "数据库恢复完成"
```

---

## 📞 技术支持

### 获取帮助

1. **查看日志**: 首先检查应用和数据库日志
2. **健康检查**: 使用健康检查端点验证服务状态
3. **文档参考**: 查阅本文档的相关章节
4. **社区支持**: 在项目仓库提交Issue

### 报告问题

提交问题时请包含以下信息：

1. **环境信息**:
   - 操作系统版本
   - Docker版本
   - 服务版本

2. **错误信息**:
   - 完整的错误日志
   - 重现步骤
   - 期望行为

3. **系统状态**:
   - 服务运行状态
   - 资源使用情况
   - 网络连接状态

### 联系方式

- **项目仓库**: [GitHub Repository]
- **文档更新**: 请提交Pull Request
- **安全问题**: 请通过私有渠道报告

---

## 📋 快速参考卡

### 常用命令

```bash
# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f record-api-service

# 健康检查
curl http://localhost:5003/health

# 重启服务
docker-compose restart record-api-service

# 停止服务
docker-compose down
```

### 重要端点

| 端点 | 方法 | 说明 |
|------|------|------|
| `/health` | GET | 基础健康检查 |
| `/api/v1/health` | GET | API健康检查 |
| `/api/v1/users` | GET/POST | 用户管理 |
| `/api/v1/conversations` | GET/POST | 对话管理 |
| `/api/v1/messages` | GET/POST | 消息管理 |
| `/api/v1/messages/{id}/urls` | PUT | 更新MinIO URL |
| `/api/v1/messages/{id}/content` | DELETE | 清理MinIO内容 |

### 默认配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| 服务端口 | 5003 | API服务端口 |
| 数据库端口 | 3307 | MySQL数据库端口 |
| 管理员API密钥 | admin-api-key-change-in-production | 默认管理员密钥 |
| 分页大小 | 20 | 默认每页记录数 |
| 最大分页大小 | 100 | 最大每页记录数 |

---

*本文档最后更新时间: 2024-01-01*
*版本: v1.1.0 (支持MinIO URL字段)*
```
