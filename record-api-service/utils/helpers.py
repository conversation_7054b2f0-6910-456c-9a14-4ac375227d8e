"""
辅助工具函数
"""
from flask import current_app
import math

def paginate_query(query, page, per_page):
    """
    分页查询辅助函数
    
    Args:
        query: SQLAlchemy查询对象
        page: 页码
        per_page: 每页数量
        
    Returns:
        dict: 包含分页数据和元信息的字典
    """
    # 限制每页最大数量
    max_per_page = current_app.config.get('MAX_ITEMS_PER_PAGE', 100)
    per_page = min(per_page, max_per_page)
    
    # 执行分页查询
    pagination = query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )
    
    return {
        'items': pagination.items,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': pagination.total,
            'pages': pagination.pages,
            'has_prev': pagination.has_prev,
            'has_next': pagination.has_next,
            'prev_num': pagination.prev_num,
            'next_num': pagination.next_num
        }
    }

def validate_json_data(request, required_fields=None):
    """
    验证JSON请求数据
    
    Args:
        request: Flask请求对象
        required_fields: 必需字段列表
        
    Returns:
        dict: 验证后的数据
        
    Raises:
        ValueError: 数据验证失败
    """
    if not request.is_json:
        raise ValueError('请求必须是JSON格式')
    
    data = request.get_json()
    if not data:
        raise ValueError('请求数据不能为空')
    
    if required_fields:
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValueError(f'缺少必需字段: {", ".join(missing_fields)}')
    
    return data

def format_error_response(error_message, status_code=400):
    """
    格式化错误响应
    
    Args:
        error_message: 错误消息
        status_code: HTTP状态码
        
    Returns:
        tuple: (响应数据, 状态码)
    """
    return {
        'success': False,
        'error': str(error_message)
    }, status_code

def format_success_response(data=None, message=None):
    """
    格式化成功响应
    
    Args:
        data: 响应数据
        message: 成功消息
        
    Returns:
        dict: 响应数据
    """
    response = {'success': True}
    
    if data is not None:
        response['data'] = data
    
    if message:
        response['message'] = message
    
    return response

def calculate_cost(prompt_tokens, completion_tokens, prompt_price_per_1k, completion_price_per_1k):
    """
    计算API调用成本
    
    Args:
        prompt_tokens: 输入token数量
        completion_tokens: 输出token数量
        prompt_price_per_1k: 每1000个输入token的价格
        completion_price_per_1k: 每1000个输出token的价格
        
    Returns:
        dict: 包含各项成本的字典
    """
    prompt_cost = (prompt_tokens / 1000) * prompt_price_per_1k if prompt_tokens else 0
    completion_cost = (completion_tokens / 1000) * completion_price_per_1k if completion_tokens else 0
    total_cost = prompt_cost + completion_cost
    
    return {
        'prompt_cost': round(prompt_cost, 6),
        'completion_cost': round(completion_cost, 6),
        'total_cost': round(total_cost, 6)
    }

def safe_float(value, default=0.0):
    """
    安全转换为浮点数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        float: 转换后的浮点数
    """
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """
    安全转换为整数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        int: 转换后的整数
    """
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def truncate_text(text, max_length=100, suffix='...'):
    """
    截断文本
    
    Args:
        text: 要截断的文本
        max_length: 最大长度
        suffix: 后缀
        
    Returns:
        str: 截断后的文本
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def get_client_ip(request):
    """
    获取客户端IP地址
    
    Args:
        request: Flask请求对象
        
    Returns:
        str: 客户端IP地址
    """
    # 检查代理头
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr
