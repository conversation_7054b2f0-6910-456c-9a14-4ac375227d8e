"""
用户管理API蓝图
"""
from flask import Blueprint, request, jsonify
from sqlalchemy import desc, asc
from models import db, User
from utils.helpers import paginate_query, validate_json_data
from utils.auth import require_api_key
from decimal import Decimal
import uuid

users_bp = Blueprint('users', __name__)

@users_bp.route('/users', methods=['GET'])
@require_api_key
def get_users():
    """获取用户列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        is_active = request.args.get('is_active', type=bool)
        permission = request.args.get('permission', type=int)
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        # 构建查询
        query = User.query
        
        # 过滤条件
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        if permission is not None:
            query = query.filter(User.permission == permission)
            
        # 排序
        if sort_order == 'desc':
            query = query.order_by(desc(getattr(User, sort_by, User.created_at)))
        else:
            query = query.order_by(asc(getattr(User, sort_by, User.created_at)))
        
        # 分页
        result = paginate_query(query, page, per_page)
        
        return jsonify({
            'success': True,
            'data': {
                'users': [user.to_dict() for user in result['items']],
                'pagination': result['pagination']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@users_bp.route('/users/<int:user_id>', methods=['GET'])
@require_api_key
def get_user(user_id):
    """获取单个用户信息"""
    try:
        user = User.query.get_or_404(user_id)
        return jsonify({
            'success': True,
            'data': user.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@users_bp.route('/users', methods=['POST'])
@require_api_key
def create_user():
    """创建新用户"""
    try:
        data = validate_json_data(request, required_fields=['api_key'])
        
        # 检查API密钥是否已存在
        if User.query.filter_by(api_key=data['api_key']).first():
            return jsonify({
                'success': False,
                'error': 'API密钥已存在'
            }), 400
        
        # 创建用户
        user = User(
            api_key=data['api_key'],
            is_active=data.get('is_active', True),
            permission=data.get('permission', 1),
            mathjax=data.get('mathjax', False),
            current_model_id=data.get('current_model_id'),
            current_temperature=Decimal(str(data.get('current_temperature', 0.70))),
            total_deposited=Decimal(str(data.get('total_deposited', 0.0))),
            total_spent=Decimal(str(data.get('total_spent', 0.0))),
            current_balance=Decimal(str(data.get('current_balance', 0.0)))
        )
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': user.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@users_bp.route('/users/<int:user_id>', methods=['PUT'])
@require_api_key
def update_user(user_id):
    """更新用户信息"""
    try:
        user = User.query.get_or_404(user_id)
        data = validate_json_data(request)
        
        # 更新字段
        if 'is_active' in data:
            user.is_active = data['is_active']
        if 'permission' in data:
            user.permission = data['permission']
        if 'mathjax' in data:
            user.mathjax = data['mathjax']
        if 'current_model_id' in data:
            user.current_model_id = data['current_model_id']
        if 'current_temperature' in data:
            user.current_temperature = Decimal(str(data['current_temperature']))
        if 'current_conversation_id' in data:
            user.current_conversation_id = data['current_conversation_id']
        if 'total_deposited' in data:
            user.total_deposited = Decimal(str(data['total_deposited']))
        if 'total_spent' in data:
            user.total_spent = Decimal(str(data['total_spent']))
        if 'current_balance' in data:
            user.current_balance = Decimal(str(data['current_balance']))
        if 'total_prompt_tokens' in data:
            user.total_prompt_tokens = data['total_prompt_tokens']
        if 'total_completion_tokens' in data:
            user.total_completion_tokens = data['total_completion_tokens']
            
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@users_bp.route('/users/<int:user_id>', methods=['DELETE'])
@require_api_key
def delete_user(user_id):
    """删除用户"""
    try:
        user = User.query.get_or_404(user_id)
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '用户已删除'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@users_bp.route('/users/by-api-key/<api_key>', methods=['GET'])
@require_api_key
def get_user_by_api_key(api_key):
    """根据API密钥获取用户信息"""
    try:
        user = User.query.filter_by(api_key=api_key).first_or_404()
        return jsonify({
            'success': True,
            'data': user.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@users_bp.route('/users/<int:user_id>/stats', methods=['GET'])
@require_api_key
def get_user_stats(user_id):
    """获取用户统计信息"""
    try:
        user = User.query.get_or_404(user_id)
        
        # 计算统计信息
        conversation_count = len(user.conversations)
        total_messages = sum(len(conv.messages) for conv in user.conversations)
        
        stats = {
            'user_id': user.id,
            'conversation_count': conversation_count,
            'total_messages': total_messages,
            'total_prompt_tokens': user.total_prompt_tokens,
            'total_completion_tokens': user.total_completion_tokens,
            'total_tokens': user.total_prompt_tokens + user.total_completion_tokens,
            'total_deposited': float(user.total_deposited) if user.total_deposited else 0.0,
            'total_spent': float(user.total_spent) if user.total_spent else 0.0,
            'current_balance': float(user.current_balance) if user.current_balance else 0.0
        }
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
