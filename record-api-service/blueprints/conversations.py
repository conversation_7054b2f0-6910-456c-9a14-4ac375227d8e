"""
对话管理API蓝图
"""
from flask import Blueprint, request, jsonify
from sqlalchemy import desc, asc
from models import db, Conversation, User, Message
from utils.helpers import paginate_query, validate_json_data
from utils.auth import require_api_key
from datetime import datetime

conversations_bp = Blueprint('conversations', __name__)

@conversations_bp.route('/conversations', methods=['GET'])
@require_api_key
def get_conversations():
    """获取对话列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        user_id = request.args.get('user_id', type=int)
        sort_by = request.args.get('sort_by', 'latest_revised_at')
        sort_order = request.args.get('sort_order', 'desc')
        include_messages = request.args.get('include_messages', 'false').lower() == 'true'
        
        # 构建查询
        query = Conversation.query
        
        # 过滤条件
        if user_id:
            query = query.filter(Conversation.user_id == user_id)
            
        # 排序
        if sort_order == 'desc':
            query = query.order_by(desc(getattr(Conversation, sort_by, Conversation.latest_revised_at)))
        else:
            query = query.order_by(asc(getattr(Conversation, sort_by, Conversation.latest_revised_at)))
        
        # 分页
        result = paginate_query(query, page, per_page)
        
        return jsonify({
            'success': True,
            'data': {
                'conversations': [conv.to_dict(include_messages=include_messages) for conv in result['items']],
                'pagination': result['pagination']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@conversations_bp.route('/conversations/<int:conversation_id>', methods=['GET'])
@require_api_key
def get_conversation(conversation_id):
    """获取单个对话信息"""
    try:
        include_messages = request.args.get('include_messages', 'true').lower() == 'true'
        conversation = Conversation.query.get_or_404(conversation_id)
        
        return jsonify({
            'success': True,
            'data': conversation.to_dict(include_messages=include_messages)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@conversations_bp.route('/conversations', methods=['POST'])
@require_api_key
def create_conversation():
    """创建新对话"""
    try:
        data = validate_json_data(request, required_fields=['user_id'])
        
        # 验证用户是否存在
        user = User.query.get_or_404(data['user_id'])
        
        # 创建对话
        conversation = Conversation(
            user_id=data['user_id'],
            title=data.get('title', f'对话 {datetime.now().strftime("%Y-%m-%d %H:%M")}')
        )
        
        db.session.add(conversation)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': conversation.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@conversations_bp.route('/conversations/<int:conversation_id>', methods=['PUT'])
@require_api_key
def update_conversation(conversation_id):
    """更新对话信息"""
    try:
        conversation = Conversation.query.get_or_404(conversation_id)
        data = validate_json_data(request)
        
        # 更新字段
        if 'title' in data:
            conversation.title = data['title']
        
        # 更新修改时间
        conversation.latest_revised_at = datetime.utcnow()
            
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': conversation.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@conversations_bp.route('/conversations/<int:conversation_id>', methods=['DELETE'])
@require_api_key
def delete_conversation(conversation_id):
    """删除对话"""
    try:
        conversation = Conversation.query.get_or_404(conversation_id)
        db.session.delete(conversation)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '对话已删除'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@conversations_bp.route('/users/<int:user_id>/conversations', methods=['GET'])
@require_api_key
def get_user_conversations(user_id):
    """获取用户的对话列表"""
    try:
        # 验证用户是否存在
        user = User.query.get_or_404(user_id)
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        sort_by = request.args.get('sort_by', 'latest_revised_at')
        sort_order = request.args.get('sort_order', 'desc')
        include_messages = request.args.get('include_messages', 'false').lower() == 'true'
        
        # 构建查询
        query = Conversation.query.filter(Conversation.user_id == user_id)
            
        # 排序
        if sort_order == 'desc':
            query = query.order_by(desc(getattr(Conversation, sort_by, Conversation.latest_revised_at)))
        else:
            query = query.order_by(asc(getattr(Conversation, sort_by, Conversation.latest_revised_at)))
        
        # 分页
        result = paginate_query(query, page, per_page)
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'conversations': [conv.to_dict(include_messages=include_messages) for conv in result['items']],
                'pagination': result['pagination']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@conversations_bp.route('/conversations/<int:conversation_id>/messages', methods=['GET'])
@require_api_key
def get_conversation_messages(conversation_id):
    """获取对话的消息列表"""
    try:
        # 验证对话是否存在
        conversation = Conversation.query.get_or_404(conversation_id)
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 50, type=int), 100)
        sort_order = request.args.get('sort_order', 'asc')
        
        # 构建查询
        query = Message.query.filter(Message.conversation_id == conversation_id)
            
        # 排序
        if sort_order == 'desc':
            query = query.order_by(desc(Message.created_at))
        else:
            query = query.order_by(asc(Message.created_at))
        
        # 分页
        result = paginate_query(query, page, per_page)
        
        return jsonify({
            'success': True,
            'data': {
                'conversation_id': conversation_id,
                'messages': [msg.to_dict() for msg in result['items']],
                'pagination': result['pagination']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@conversations_bp.route('/conversations/<int:conversation_id>/stats', methods=['GET'])
@require_api_key
def get_conversation_stats(conversation_id):
    """获取对话统计信息"""
    try:
        conversation = Conversation.query.get_or_404(conversation_id)
        
        # 计算统计信息
        messages = conversation.messages
        message_count = len(messages)
        total_prompt_tokens = sum(msg.prompt_tokens or 0 for msg in messages)
        total_completion_tokens = sum(msg.completion_tokens or 0 for msg in messages)
        total_cost = sum(float(msg.total_cost or 0) for msg in messages)
        error_count = sum(1 for msg in messages if msg.is_error)
        
        stats = {
            'conversation_id': conversation.id,
            'title': conversation.title,
            'message_count': message_count,
            'total_prompt_tokens': total_prompt_tokens,
            'total_completion_tokens': total_completion_tokens,
            'total_tokens': total_prompt_tokens + total_completion_tokens,
            'total_cost': total_cost,
            'error_count': error_count,
            'created_at': conversation.created_at.isoformat() if conversation.created_at else None,
            'latest_revised_at': conversation.latest_revised_at.isoformat() if conversation.latest_revised_at else None
        }
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
