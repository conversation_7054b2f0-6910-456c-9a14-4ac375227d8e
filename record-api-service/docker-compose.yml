services:
  record-api-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: record-api-service
    restart: unless-stopped
    ports:
      - "5003:5003"
    env_file:
      - .env
    environment:
      FLASK_ENV: production
      PYTHONUNBUFFERED: 1
      SERVICE_TYPE: record-api-service
    volumes:
      - record_api_logs:/app/logs
      - record_api_uploads:/app/uploads
    networks:
      - shared-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5003/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 记录数据库服务（可选，如果需要在同一个compose文件中启动）
  # records-db:
  #   image: mysql:8.0
  #   container_name: records-db
  #   restart: unless-stopped
  #   environment:
  #     MYSQL_ROOT_PASSWORD: records_root_password
  #     MYSQL_DATABASE: vdb_records
  #     MYSQL_USER: records_user
  #     MYSQL_PASSWORD: records_password
  #   ports:
  #     - "3307:3306"
  #   volumes:
  #     - /vdb_records:/var/lib/mysql
  #   networks:
  #     - shared-network

networks:
  shared-network:
    name: llm-system-network
    external: true

volumes:
  record_api_logs:
    driver: local
  record_api_uploads:
    driver: local
