# app.py - LLM 安全代理API服务
from flask import Flask, jsonify
from models import db
from config import get_config
from utils.logging_config import setup_logging
from utils.error_handlers import register_error_handlers
from blueprints.api import api_bp
import os
from dotenv import load_dotenv

def load_environment():
    """根据FLASK_ENV加载相应的环境变量文件"""
    env_file = f".env.{os.environ.get('FLASK_ENV', 'development')}"
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"Loaded environment from {env_file}")
    elif os.path.exists('.env'):
        load_dotenv('.env')
        print("Loaded environment from .env")
    else:
        print("No .env file found, using system environment variables")

load_environment()

def create_app(config_name=None):
    """应用工厂函数 - LLM 安全代理API服务"""
    app = Flask(__name__)

    # 加载配置
    config_class = get_config()
    app.config.from_object(config_class)

    # 初始化扩展
    db.init_app(app)

    # 设置日志
    setup_logging(app)

    # 注册错误处理器
    register_error_handlers(app)

    # 注册API蓝图
    app.register_blueprint(api_bp)

    # 根路径提供服务信息
    @app.route('/')
    def index():
        return jsonify({
            'service': 'Secure LLM API Proxy Service',
            'version': app.config.get('API_VERSION', 'v1'),
            'status': 'running',
            'endpoints': {
                'health_check': '/api/v1/health',
                'get_models': '/api/v1/models',
                'chat_completions': '/api/v1/chat/completions'
            },
            'documentation': 'Provides secure, key-based access to Large Language Models.'
        })

    # 在应用上下文中创建数据库表
    with app.app_context():
        db.create_all()

    return app

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5002)
