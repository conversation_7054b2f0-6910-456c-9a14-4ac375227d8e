
services:
  model-api-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: model-api-service
    restart: unless-stopped
    ports:
      - "5002:5002"
    env_file:
      - .env
    environment:
      FLASK_ENV: production
      PYTHONUNBUFFERED: 1
      SERVICE_TYPE: model-api-service
    volumes:
      - model_api_logs:/app/logs
      - model_api_uploads:/app/uploads
    networks:
      - shared-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  shared-network:
    name: llm-system-network
    external: true

volumes:
  model_api_logs:
    driver: local
  model_api_uploads:
    driver: local
