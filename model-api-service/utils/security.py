import hashlib
import secrets
import time
from functools import wraps
from flask import request, jsonify, current_app, g
from werkzeug.exceptions import Unauthorized
from services.application_service import ApplicationService

def api_key_required(f):
    """
    API密钥验证装饰器。
    从 'Authorization: Bearer <API_KEY>' 头中提取密钥，
    验证它，并将关联的应用对象附加到 g.application。
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 1. 检查API功能是否已启用
        if not current_app.config.get('API_ENABLED', False):
            raise Unauthorized("API functionality is disabled.")

        # 2. 从 Authorization 头获取API密钥
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            raise Unauthorized("Authorization header is missing or invalid. Use 'Bearer <API_KEY>'.")
        
        api_key = auth_header.split(' ')[1]
        
        # 3. 验证API密钥
        application = ApplicationService.get_by_api_key(api_key)
        
        # 4. 检查应用是否存在且处于活动状态
        if not application or not application.is_active:
            raise Unauthorized("Invalid or inactive API Key.")
            
        # 5. 将应用对象附加到Flask的g对象，以便在视图函数中访问
        g.application = application
        
        return f(*args, **kwargs)
    return decorated_function

def rate_limit(max_requests: int = 100, window: int = 3600):
    """简单的速率限制装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 这里可以实现更复杂的速率限制逻辑
            # 暂时只是一个占位符
            return f(*args, **kwargs)
        return decorated_function
    return decorator
