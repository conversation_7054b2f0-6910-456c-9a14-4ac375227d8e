from flask import jsonify, request
from werkzeug.exceptions import HTTPException
from models import db
import logging
import traceback

logger = logging.getLogger(__name__)

def register_error_handlers(app):
    """
    注册全局错误处理器，确保所有错误都返回JSON响应。
    """

    @app.errorhandler(HTTPException)
    def handle_http_exception(e):
        """处理所有HTTP异常 (e.g., 404, 401, 400)"""
        logger.warning(f"HTTP Exception: {e.code} {e.name} for {request.url}. Description: {e.description}")
        response = {
            "success": False,
            "error": {
                "code": e.code,
                "name": e.name,
                "description": e.description,
            }
        }
        return jsonify(response), e.code

    @app.errorhandler(Exception)
    def handle_generic_exception(e):
        """处理所有未捕获的服务器内部异常 (500)"""
        # 生成一个唯一的错误ID以便追踪
        error_id = f"ERR_{hash(str(e) + traceback.format_exc())}"
        
        logger.critical(f"Unhandled Exception (ID: {error_id}): {e}", exc_info=True)

        # 任何未知异常都应尝试回滚数据库会话，以防数据状态不一致
        try:
            db.session.rollback()
            logger.info(f"Database session rolled back for error {error_id}.")
        except Exception as db_err:
            logger.error(f"Failed to rollback database session for error {error_id}: {db_err}")

        response = {
            "success": False,
            "error": {
                "code": 500,
                "name": "Internal Server Error",
                "description": (
                    "The server encountered an unexpected condition that prevented it from "
                    f"fulfilling the request. Please reference this error ID for support: {error_id}"
                ),
            }
        }
        return jsonify(response), 500
