# API服务专用验证工具
# 纯API服务不需要表单验证，只需要JSON输入验证

def validate_json_input(data, required_fields=None):
    """验证JSON输入"""
    if not isinstance(data, dict):
        return False, "输入必须是JSON对象"
    
    if required_fields:
        for field in required_fields:
            if field not in data:
                return False, f"缺少必需字段: {field}"
    
    return True, "验证通过"

def sanitize_string(input_str):
    """清理字符串输入"""
    if not input_str:
        return ""
    
    # 移除潜在的危险字符
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
    for char in dangerous_chars:
        input_str = input_str.replace(char, '')
    
    return input_str.strip()
