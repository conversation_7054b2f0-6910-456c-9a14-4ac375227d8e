from models import AIModel

class ModelService:
    """模型服务 - 只读操作"""

    @staticmethod
    def get_all(page=1, per_page=20, platform_id=None, visible_only=False, free_only=False):
        """获取所有模型（分页和过滤）"""
        query = AIModel.query

        if platform_id:
            query = query.filter_by(platform_id=platform_id)
        if visible_only:
            query = query.filter_by(is_visible_model=True)
        if free_only:
            query = query.filter_by(free=True)

        return query.paginate(page=page, per_page=per_page, error_out=False)

    @staticmethod
    def get_by_id(model_id):
        """根据ID获取模型"""
        return AIModel.query.get(model_id)

    @staticmethod
    def get_by_display_name(display_name):
        """根据显示名称获取模型"""
        return AIModel.query.filter_by(display_name=display_name).first()