from models import AppModel

class AppModelService:
    """应用模型关联服务 - 只读操作"""

    @staticmethod
    def get_all(page=1, per_page=20):
        """获取所有应用模型关联（分页）"""
        return AppModel.query.paginate(
            page=page, per_page=per_page, error_out=False
        )

    @staticmethod
    def get_by_id(app_model_id):
        """根据ID获取应用模型关联"""
        return AppModel.query.get(app_model_id)

    @staticmethod
    def get_by_app_id(app_id):
        """根据应用ID获取所有关联模型"""
        return AppModel.query.filter_by(application_id=app_id).all()

    @staticmethod
    def get_by_app_and_model(app_id, model_id):
        """根据应用ID和模型ID获取关联"""
        return AppModel.query.filter_by(
            application_id=app_id,
            model_id=model_id
        ).first()