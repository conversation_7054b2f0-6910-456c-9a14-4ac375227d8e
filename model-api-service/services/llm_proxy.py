import openai
import json
import logging
from flask import Response

logger = logging.getLogger(__name__)

class LLMProxyService:
    """使用OpenAI SDK与任何兼容OpenAI的后端进行通信的代理服务。"""

    def __init__(self, model, platform):
        self.model = model
        self.platform = platform
        # 根据从数据库中为该模型平台获取的信息，动态配置OpenAI客户端
        self.client = openai.OpenAI(
            base_url=self.platform.base_url,
            api_key=self.platform.api_key,
            timeout=180.0,
        )

    def _stream_response(self, response_stream):
        """处理和格式化流式响应的生成器"""
        try:
            for chunk in response_stream:
                # 将pydantic模型块转换为JSON字符串，并遵循SSE格式
                yield f"data: {chunk.model_dump_json()}\n\n"
        finally:
            # 确保流被正确关闭
             response_stream.close()
        # 发送SSE协议的结束信号
        yield "data: [DONE]\n\n"

    def create_completion(self, data):
        """创建并代理聊天补全请求"""
        
        stream = data.get('stream', False)
        
        logger.info(
            f"Proxying request via OpenAI SDK to base_url: {self.platform.base_url} for model "
            f"'{self.model.internal_name}' (Stream: {stream})"
        )

        try:
            # 直接将参数传递给强大的OpenAI SDK，它会处理好一切
            response = self.client.chat.completions.create(
                model=self.model.internal_name,
                messages=data.get('messages', []),
                stream=stream,
                temperature=data.get('temperature', 0.7),
                max_tokens=data.get('max_tokens', 2048),
            )

            if stream:
                # 返回一个符合SSE规范的流式响应
                return Response(self._stream_response(response), mimetype='text/event-stream')
            else:
                # 返回一个标准的JSON响应
                return Response(
                    response.model_dump_json(indent=2),
                    status=200,
                    mimetype='application/json'
                )

        except openai.APIStatusError as e:
            # 处理来自API的特定HTTP错误 (e.g., 4xx, 5xx)
            logger.error(f"OpenAI APIStatusError: {e.status_code} - {e.response.text}")
            return Response(json.dumps({
                'success': False,
                'error': {
                    'type': 'downstream_api_error',
                    'message': e.message if hasattr(e, 'message') else 'An error occurred at the downstream service.',
                    'code': e.status_code
                }
            }), status=e.status_code, mimetype='application/json')
            
        except openai.APIConnectionError as e:
            # 处理底层的连接错误
            logger.error(f"OpenAI APIConnectionError: {e.__cause__}")
            return Response(json.dumps({
                'success': False,
                'error': {
                    'type': 'downstream_connection_error',
                    'message': 'Failed to connect to the LLM backend.',
                }
            }), status=503, mimetype='application/json')

        except Exception as e:
            # 捕获任何其他意外错误，以防万一
            logger.critical(f"Unexpected error in LLMProxyService: {e}", exc_info=True)
            return Response(json.dumps({
                'success': False,
                'error': {
                    'type': 'internal_proxy_error',
                    'message': 'An unexpected error occurred within the API proxy service.',
                }
            }), status=500, mimetype='application/json')