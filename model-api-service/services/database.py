from models import Platform, AIModel, Application, AppModel
import logging

logger = logging.getLogger(__name__)

class ModelService:
    """模型服务 - 只读操作"""
    @staticmethod
    def get_by_id(model_id):
        """根据ID获取模型"""
        return AIModel.query.get(model_id)
    
    @staticmethod
    def get_by_display_name(display_name):
        """根据显示名称获取模型"""
        return AIModel.query.filter_by(display_name=display_name).first()

class ApplicationService:
    """应用服务 - 只读操作"""

    @staticmethod
    def get_all(page=1, per_page=20):
        """获取所有应用（分页）"""
        return Application.query.filter_by(is_active=True).paginate(
            page=page, per_page=per_page, error_out=False
        )

    @staticmethod
    def get_by_id(app_id):
        """根据ID获取应用"""
        return Application.query.get(app_id)

    @staticmethod
    def get_by_name(name):
        """根据名称获取应用"""
        return Application.query.filter_by(name=name).first()

    @staticmethod
    def get_by_api_key(api_key):
        """根据API密钥获取应用"""
        return Application.query.filter_by(api_key=api_key, is_active=True).first()

class AppModelService:
    """应用模型关联服务 - 只读操作"""

    @staticmethod
    def get_all(page=1, per_page=20):
        """获取所有应用模型关联（分页）"""
        return AppModel.query.paginate(
            page=page, per_page=per_page, error_out=False
        )

    @staticmethod
    def get_by_id(app_model_id):
        """根据ID获取应用模型关联"""
        return AppModel.query.get(app_model_id)

    @staticmethod
    def get_by_app_id(app_id):
        """根据应用ID获取所有关联模型"""
        return AppModel.query.filter_by(application_id=app_id).all()

    @staticmethod
    def get_by_app_and_model(app_id, model_id):
        """根据应用ID和模型ID获取关联"""
        return AppModel.query.filter_by(
            application_id=app_id,
            model_id=model_id
        ).first()
