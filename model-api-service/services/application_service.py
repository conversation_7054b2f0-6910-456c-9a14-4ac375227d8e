from models import Application

class ApplicationService:
    """应用服务 - 只读操作"""

    @staticmethod
    def get_all(page=1, per_page=20):
        """获取所有应用（分页）"""
        return Application.query.filter_by(is_active=True).paginate(
            page=page, per_page=per_page, error_out=False
        )

    @staticmethod
    def get_by_id(app_id):
        """根据ID获取应用"""
        return Application.query.get(app_id)

    @staticmethod
    def get_by_name(name):
        """根据名称获取应用"""
        return Application.query.filter_by(name=name).first()

    @staticmethod
    def get_by_api_key(api_key):
        """根据API密钥获取应用"""
        return Application.query.filter_by(api_key=api_key, is_active=True).first()