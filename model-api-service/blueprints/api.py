from flask import Blueprint, jsonify, request, current_app, g
from werkzeug.exceptions import Unauthorized, NotFound, BadRequest
from services.application_service import ApplicationService
from services.app_model_service import AppModelService
from services.model_service import ModelService
from services.llm_proxy import LLMProxyService
from utils.security import api_key_required, rate_limit
import logging

logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

@api_bp.route('/models', methods=['GET'])
@api_key_required
@rate_limit(max_requests=100, window=3600)
def get_available_models():
    """
    获取当前应用API Key可用的模型列表
    """
    # g.application 由 @api_key_required 装饰器设置
    application = g.get('application')
    if not application:
        raise Unauthorized("Invalid application context.")
        
    app_models = AppModelService.get_by_app_id(application.id)
    
    models_data = []
    for am in app_models:
        # 只返回客户端需要的最少信息
        model_info = {
            'id': am.model.id,
            'name': am.model.display_name,
            'is_default': am.is_default
        }
        models_data.append(model_info)

    return jsonify({
        'success': True,
        'application': application.name,
        'models': models_data
    })


@api_bp.route('/chat/completions', methods=['POST'])
@api_key_required
@rate_limit(max_requests=1000, window=3600)
def chat_completions():
    """
    代理聊天补全请求到相应的大语言模型服务
    """
    # 1. 获取请求数据和应用上下文
    data = request.get_json()
    if not data:
        raise BadRequest("JSON payload is missing.")
        
    application = g.get('application')
    if not application:
        raise Unauthorized("Invalid application context.")

    # 2. 确定要使用的模型
    requested_model_name = data.get('model')
    
    app_models = AppModelService.get_by_app_id(application.id)
    
    target_model_assoc = None
    if requested_model_name:
        # 如果客户端指定了模型，查找该模型是否已授权
        target_model_assoc = next((am for am in app_models if am.model.display_name == requested_model_name), None)
        if not target_model_assoc:
            raise BadRequest(f"Model '{requested_model_name}' is not authorized for this application.")
    else:
        # 如果客户端未指定模型，查找默认模型
        target_model_assoc = next((am for am in app_models if am.is_default), None)
        if not target_model_assoc:
            raise BadRequest("No default model is configured for this application and no model was specified.")

    # 3. 获取模型和平台详细信息
    final_model = target_model_assoc.model
    platform = final_model.platform # 假设 AppModel.model.platform 总是存在
    if not platform:
        # 引发一个标准异常，让全局处理器捕获
        raise Exception(f"Platform information is missing for model ID {final_model.id}")
    
    # 4. 创建LLM代理服务并处理请求
    proxy_service = LLMProxyService(model=final_model, platform=platform)
    
    # 将原始请求体传递给代理服务
    return proxy_service.create_completion(data)


@api_bp.route('/health', methods=['GET'])
def api_health():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'version': current_app.config.get('API_VERSION', 'v1'),
        'api_enabled': current_app.config.get('API_ENABLED', False)
    })
