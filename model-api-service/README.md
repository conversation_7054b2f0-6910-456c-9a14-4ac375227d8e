# 安全LLM代理API服务

这是大语言模型管理系统的核心组件之一：一个安全的API代理服务，运行在5002端口。

## 核心功能

- **安全认证**：所有请求必须通过 `Authorization: Bearer <API_KEY>` 头进行认证。API密钥与特定的"应用"关联。
- **模型授权**：每个应用只能访问其被授权的模型列表。客户端不能访问未经授权的模型。
- **LLM请求代理**：服务将客户端的聊天请求安全地代理到后端的各种大语言模型服务（如OpenAI, Anthropic等）。
- **统一接口**：为所有下游LLM服务提供一个统一、稳定的 `chat/completions` 接口。
- **流式响应**：完全支持流式（streaming）响应，允许客户端实时接收数据。

## API 端点

### `GET /api/v1/models`

获取当前 `API Key` 所授权访问的模型列表。

- **认证**: `Authorization: Bearer YOUR_API_KEY` (必需)
- **成功响应 (200 OK)**:
  ```json
  {
    "success": true,
    "application": "Your Application Name",
    "models": [
      {
        "id": 1,
        "name": "gpt-4-turbo",
        "is_default": true
      },
      {
        "id": 5,
        "name": "claude-3-opus",
        "is_default": false
      }
    ]
  }
  ```

### `POST /api/v1/chat/completions`

发送聊天请求到LLM。服务将根据请求的 `model` 或应用的默认模型，将请求代理到正确的下游服务。

- **认证**: `Authorization: Bearer YOUR_API_KEY` (必需)
- **请求体 (JSON)**:
  ```json
  {
    "model": "gpt-4-turbo", // (可选) 如果不提供，将使用应用的默认模型
    "messages": [
      {"role": "user", "content": "你好，请介绍一下自己。"}
    ],
    "stream": false, // (可选) 默认为 false。如果为 true，将以流式返回响应。
    "temperature": 0.7, // (可选)
    "max_tokens": 1024 // (可选)
  }
  ```
- **响应**:
  - 如果 `stream: false`，直接返回下游LLM服务的JSON响应。
  - 如果 `stream: true`，返回一个 `text/event-stream` 类型的流式响应。

### `GET /api/v1/health`

服务健康检查。

### Bash 调用示例

1.  **获取可用模型**:
    ```bash
    curl -X GET "http://localhost:5002/api/v1/models" \
         -H "Authorization: Bearer YOUR_APPLICATION_API_KEY"
    ```

2.  **发送聊天请求 (非流式)**:
    ```bash
    curl -X POST "http://localhost:5002/api/v1/chat/completions" \
         -H "Content-Type: application/json" \
         -H "Authorization: Bearer YOUR_APPLICATION_API_KEY" \
         -d '{
           "model": "gpt-4-turbo",
           "messages": [
             {"role": "user", "content": "Hello!"}
           ]
         }'
    ```

3.  **发送聊天请求 (流式)**:
    ```bash
    curl -X POST "http://localhost:5002/api/v1/chat/completions" \
         -H "Content-Type: application/json" \
         -H "Authorization: Bearer YOUR_APPLICATION_API_KEY" \
         -d '{
           "messages": [
             {"role": "user", "content": "Tell me a joke."}
           ],
           "stream": true
         }' --no-buffer
    ```

### Python 调用示例

1. **获取可用模型**:
```python
import requests

API_KEY = "YOUR_APPLICATION_API_KEY"
BASE_URL = "http://localhost:5002/api/v1"

headers = {
    "Authorization": f"Bearer {API_KEY}"
}

response = requests.get(f"{BASE_URL}/models", headers=headers)
print(response.json())
```

2. **发送聊天请求 (非流式)**:
```python
import requests

API_KEY = "YOUR_APPLICATION_API_KEY"
BASE_URL = "http://localhost:5002/api/v1"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

payload = {
    "model": "gpt-4-turbo",
    "messages": [
        {"role": "user", "content": "你好，请介绍一下自己。"}
    ]
}

response = requests.post(f"{BASE_URL}/chat/completions", headers=headers, json=payload)
print(response.json())
```

3. **发送聊天请求 (流式)**:
```python
import requests

API_KEY = "YOUR_APPLICATION_API_KEY"
BASE_URL = "http://localhost:5002/api/v1"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

payload = {
    "messages": [
        {"role": "user", "content": "讲个笑话。"}
    ],
    "stream": True
}

response = requests.post(f"{BASE_URL}/chat/completions", headers=headers, json=payload, stream=True)

# 处理流式响应
for chunk in response.iter_lines():
    if chunk:
        print(chunk.decode('utf-8'))
```

## 配置

- **端口**: 5002
- **数据库**: 连接到 `models-db` 以验证应用和模型信息。
- **核心依赖**: `Flask`, `SQLAlchemy`, `requests`, `gunicorn`, `openai`。

## 与 model-manager 服务的关系

- **model-manager (端口 5001)**: 负责管理平台、模型、应用和API密钥。所有数据的**增、删、改**操作都在此服务中完成。
- **model-api-service (端口 5002)**: 负责**安全地对外提供服务**。它只读取 `model-manager` 创建的数据，用于认证和代理请求。

这种架构分离了管理平面和数据平面，提高了系统的安全性和可伸缩性。
